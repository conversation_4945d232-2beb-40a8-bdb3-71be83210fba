# TrueDax Web Scraper

A professional, production-grade web scraping platform built with Go, featuring async job scheduling, comprehensive content extraction, and robust MongoDB storage with complete API documentation.

## 🚀 Features

### Core Functionality
- **Async Job Scheduling**: Priority-based job queue with concurrent processing
- **Comprehensive Web Scraping**: HTTP client + headless Chrome (chromedp) for JavaScript-heavy sites
- **Database-Based Storage**: MongoDB integration with official Go driver for scalable result storage
- **Content Extraction**: Headings, paragraphs, links, images, tables, forms, lists, and structured data
- **Authentication & Authorization**: JWT-based stateless auth with role-based access (user/admin)
- **Audit Logging**: Comprehensive activity tracking and security monitoring
- **Health Monitoring**: System metrics, database connectivity, and performance monitoring
- **Docker Support**: Containerized deployment with multi-stage builds and Nginx reverse proxy
- **Production-Grade API**: Complete Swagger/OpenAPI documentation with examples

### Technical Stack
- **Backend**: Go 1.21+ with Gin framework
- **Database**: MongoDB with official Go driver and connection pooling
- **Authentication**: JWT tokens with access/refresh pattern and secure token rotation
- **Web Scraping**: HTTP client + chromedp for JavaScript-heavy sites with smart detection
- **Containerization**: Docker with Nginx reverse proxy and SSL support
- **Architecture**: Clean architecture with repository pattern and service layer
- **API Documentation**: Swagger/OpenAPI 3.0 with comprehensive examples and security definitions

## 📁 Project Structure

```
├── backend/
│   ├── cmd/
│   │   └── main.go                 # Application entry point with Swagger metadata
│   ├── config/                    # Configuration files (all at same level)
│   │   ├── app.go                 # Application configuration
│   │   ├── database.go            # Database configuration
│   │   └── logger.go              # Logger configuration
│   ├── internal/
│   │   ├── services/              # Business logic services (unified)
│   │   │   ├── auth/              # Authentication service with JWT management
│   │   │   ├── scraper/           # Web scraping services (HTTP + Chrome)
│   │   │   ├── job/               # Job management and queue processing
│   │   │   ├── audit/             # Audit logging and security tracking
│   │   │   └── monitoring/        # System monitoring and health checks
│   │   ├── handler/               # HTTP request handlers with Swagger annotations
│   │   ├── middleware/            # HTTP middleware (JWT, CORS, logging)
│   │   ├── model/                 # Data models with MongoDB tags
│   │   ├── repository/            # Data access layer with MongoDB operations
│   │   └── router/                # Route definitions and middleware setup
│   ├── docs/                      # Generated Swagger documentation
│   │   ├── docs.go                # Generated Swagger Go code
│   │   ├── swagger.json           # OpenAPI 3.0 JSON specification
│   │   └── swagger.yaml           # OpenAPI 3.0 YAML specification
│   ├── go.mod                     # Go module definition
│   └── go.sum                     # Go module checksums
├── docker-compose.yml             # Docker services configuration
├── Dockerfile                     # Multi-stage application container
├── nginx/                         # Reverse proxy configuration with SSL
│   ├── nginx.conf                 # Nginx configuration
│   └── ssl/                       # SSL certificates directory
├── scripts/                       # Deployment and utility scripts
│   ├── deploy-dev.sh              # Development deployment script
│   └── deploy-prod.sh             # Production deployment script
└── README.md                      # This comprehensive documentation
```

## 🛠️ Installation & Setup

### Prerequisites
- Go 1.21+
- Docker & Docker Compose
- MongoDB (via Docker)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd tdwebscraper
   ```

2. **Environment Configuration**
   Create `.env` file:
   ```env
   # Database
   MONGO_URI=mongodb://localhost:27017
   MONGO_DATABASE=webscraper

   # JWT
   JWT_SECRET=your-super-secret-jwt-key
   JWT_ACCESS_EXPIRY=15m
   JWT_REFRESH_EXPIRY=7d

   # Server
   SERVER_PORT=8080
   SERVER_HOST=localhost

   # Admin
   ADMIN_EMAIL=<EMAIL>
   ADMIN_PASSWORD=admin123
   ```

3. **Start with Docker Compose**
   ```bash
   docker-compose up -d
   ```

4. **Manual Setup (Development)**
   ```bash
   # Start MongoDB
   docker run -d -p 27017:27017 --name mongodb mongo:latest

   # Install dependencies
   cd backend
   go mod download

   # Run the application
   go run cmd/main.go
   ```

## 📚 API Documentation

### Interactive Documentation
- **Swagger UI**: Available at `/docs` when the server is running
- **OpenAPI Specification**: Complete API documentation with examples
- **Authentication Flow**: Detailed JWT token management documentation
- **Error Responses**: Comprehensive error handling with proper HTTP status codes

### Authentication Endpoints
- `POST /auth/register` - User registration with password validation
- `POST /auth/login` - User login with account lockout protection
- `POST /auth/refresh` - Refresh access token with automatic rotation
- `POST /auth/logout` - User logout with token revocation

### Job Management Endpoints
- `POST /jobs` - Create scraping job with comprehensive configuration
- `GET /jobs` - List user jobs with pagination and filtering
- `GET /jobs/{id}` - Get detailed job information and status
- `GET /jobs/{id}/results` - Get paginated scraping results with export options
- `GET /jobs/{id}/queue-position` - Get current queue position
- `GET /jobs/stats` - Get user job statistics
- `DELETE /jobs/{id}` - Cancel/delete job
- `POST /jobs/test-scrape` - Test single URL scraping

### Admin Endpoints (Admin Role Required)
- `GET /admin/users` - List all users with filtering and search
- `GET /admin/jobs` - List all jobs across all users
- `GET /admin/audit-logs` - View system audit logs
- `GET /admin/system-logs` - View application logs
- `POST /admin/users/{id}/role` - Update user role

### Health & Monitoring
- `GET /health` - Comprehensive health check with database connectivity
- `GET /metrics` - System metrics for monitoring integration

## 🔧 Configuration

### Application Configuration (`config/app.go`)
- Server settings (host, port, timeouts)
- JWT configuration (secrets, expiry times)
- Admin user setup
- CORS and security settings

### Database Configuration (`config/database.go`)
- MongoDB connection settings
- Connection pooling
- Timeout configurations

## 🗄️ Database Schema

### MongoDB Collections

#### `users`
- User accounts with email/password authentication
- Role-based access control (user/admin)
- Account creation and last login tracking
- Password hashing with bcrypt
- Account lockout and security features

#### `refresh_tokens`
- JWT refresh token management with secure hashing
- Token expiry and automatic rotation
- Device and IP tracking for security

#### `jobs`
- Scraping job definitions with comprehensive configuration
- User association and priority-based processing
- Progress tracking and status management
- Result metadata and performance metrics
- Error handling and retry logic

#### `scraping_results`
- Comprehensive scraped content storage in structured format
- Extracted data: headings, paragraphs, links, images, tables, forms
- Custom selector results and structured data
- Performance metrics and load times
- Metadata including HTTP headers and response codes

#### `audit_logs`
- User activity tracking for security compliance
- System event logging with detailed context
- Authentication events and admin actions
- IP address and user agent tracking
- Comprehensive security monitoring

### Data Storage Benefits

**Rich Querying Capabilities:**
- Full-text search across all scraped content
- Complex filtering and aggregation queries
- Analytics and reporting on scraping performance
- Real-time data access and export

**Storage Efficiency:**
- MongoDB compression reduces storage by 60-80%
- Estimated 70KB per page vs 164KB raw JSON
- Scalable to handle millions of pages efficiently
- Optimized indexes for fast query performance

## 🚀 Deployment

### Quick Start (Development)

**Windows:**
```bash
.\scripts\deploy-dev.bat
```

**Linux/macOS:**
```bash
chmod +x scripts/deploy-dev.sh
./scripts/deploy-dev.sh
```

**Manual Docker Deployment:**
```bash
# Copy environment file
cp .env.example .env

# Start all services
docker-compose up --build -d

# Check health
curl http://localhost:9000/health

# View logs
docker-compose logs -f truedax-scraper
```

### Production Deployment

**1. Security Configuration:**
```bash
# Generate strong secrets
JWT_SECRET=$(openssl rand -base64 64)
MONGODB_PASSWORD=$(openssl rand -base64 32)

# Set production mode
GIN_MODE=release
LOG_LEVEL=warn
BCRYPT_COST=12
```

**2. SSL/TLS Setup:**
```bash
# Let's Encrypt (Recommended)
sudo certbot certonly --standalone -d your-domain.com
sudo cp /etc/letsencrypt/live/your-domain.com/fullchain.pem nginx/ssl/cert.pem
sudo cp /etc/letsencrypt/live/your-domain.com/privkey.pem nginx/ssl/key.pem

# Deploy with SSL
export COMPOSE_PROFILES=production
./scripts/deploy-prod.sh
```

**3. Database Setup:**
```bash
# External MongoDB (Recommended for production)
MONGODB_URI=************************************************************************************

# Or use Docker MongoDB with persistent storage
docker-compose up -d mongodb
```

### Kubernetes Deployment

```yaml
# k8s-deployment.yaml
apiVersion: apps/v1
kind: Deployment
metadata:
  name: truedax-scraper
spec:
  replicas: 3
  selector:
    matchLabels:
      app: truedax-scraper
  template:
    metadata:
      labels:
        app: truedax-scraper
    spec:
      containers:
      - name: truedax-scraper
        image: truedax-scraper:latest
        ports:
        - containerPort: 9000
        resources:
          limits:
            memory: "2Gi"
            cpu: "1000m"
          requests:
            memory: "512Mi"
            cpu: "250m"
        livenessProbe:
          httpGet:
            path: /health
            port: 9000
          initialDelaySeconds: 30
          periodSeconds: 10
```

### Scaling and Performance

**Horizontal Scaling:**
```bash
# Scale to multiple instances
docker-compose up --scale truedax-scraper=3 -d

# Load balancer configuration needed
```

**Performance Tuning:**
```bash
# Optimize for high load
MAX_CONCURRENT_JOBS=10
MONGODB_MAX_POOL_SIZE=100
GOGC=100

# Chrome optimization
CHROMIUM_FLAGS=--no-sandbox --disable-dev-shm-usage --disable-gpu --headless --memory-pressure-off
```

## 🔍 Web Scraping Capabilities

### Content Extraction
- **Text Content**: Headings (H1-H6), paragraphs, main content
- **Links**: URLs, anchor text, titles, targets
- **Images**: URLs, alt text, dimensions
- **Tables**: Headers and row data
- **Forms**: Fields, types, validation rules
- **Lists**: Ordered and unordered list items
- **Structured Data**: JSON-LD, microdata, RDFa

### Scraping Methods
- **HTTP Client**: Fast, lightweight for static content
- **Headless Chrome**: JavaScript execution for dynamic content
- **Smart Detection**: Automatic method selection based on content type

### Performance Features
- Concurrent job processing
- Configurable delays and timeouts
- User-agent rotation
- Error handling and retry logic
- Progress tracking and callbacks

## 🛡️ Security Features

- **Authentication**: JWT-based stateless authentication
- **Authorization**: Role-based access control
- **Audit Logging**: Comprehensive activity tracking
- **Input Validation**: Request sanitization and validation
- **Rate Limiting**: API endpoint protection
- **CORS**: Cross-origin request handling

## 📊 Monitoring & Logging

### System Monitoring
- **Health Checks**: Comprehensive health endpoints with database connectivity
- **Performance Metrics**: Response times, memory usage, CPU utilization
- **Resource Tracking**: Container resource usage and limits
- **Database Monitoring**: Connection pool status and query performance
- **Job Queue Monitoring**: Queue length, processing times, success rates

### Audit Logging
- **Authentication Events**: Login attempts, token refresh, logout events
- **Job Management**: Job creation, execution, completion, and failures
- **Admin Actions**: User role changes, system configuration updates
- **Security Events**: Failed authentication, suspicious activity
- **System Events**: Application startup, shutdown, errors, and warnings

### Log Management
```bash
# View real-time logs
docker-compose logs -f truedax-scraper

# Export logs for analysis
docker-compose logs --no-color > logs/app-$(date +%Y%m%d).log

# Log rotation (add to crontab)
0 2 * * * cd /path/to/truedax && docker-compose logs --no-color > logs/app-$(date +\%Y\%m\%d).log 2>&1
```

### Health Check Endpoints
```bash
# Basic health check
curl http://localhost:9000/health

# Detailed system status
curl http://localhost:9000/metrics

# Database connectivity check
curl http://localhost:9000/health | jq '.checks.database'
```

## 🛠️ Troubleshooting

### Common Issues

**1. Container fails to start**
```bash
# Check logs
docker-compose logs truedax-scraper

# Check resource usage
docker system df
free -h

# Verify environment variables
docker-compose exec truedax-scraper env | grep -E "(MONGODB_|JWT_|SERVER_)"
```

**2. Database connection failed**
```bash
# Check MongoDB status
docker-compose ps mongodb
docker-compose logs mongodb

# Test connection manually
docker exec -it truedax-mongodb mongosh --eval "use truedax_scraper; db.runCommand('ping')"
```

**3. Chrome/JavaScript scraping fails**
```bash
# Check Chrome installation
docker exec truedax-scraper /usr/bin/chromium-browser --version

# Test Chrome with minimal flags
docker exec truedax-scraper /usr/bin/chromium-browser --headless --no-sandbox --dump-dom https://example.com
```

**4. High memory usage**
```bash
# Monitor container resources
docker stats truedax-scraper

# Check application metrics
curl http://localhost:9000/metrics

# Reduce concurrent jobs if needed
# Edit .env: MAX_CONCURRENT_JOBS=2
```

### Performance Optimization

**Database Optimization:**
- Ensure proper indexes are created
- Monitor connection pool usage
- Use MongoDB compression
- Regular maintenance and cleanup

**Application Tuning:**
- Adjust worker pool sizes based on available resources
- Optimize Chrome flags for memory usage
- Configure appropriate timeouts
- Use caching for frequently accessed data

## 🗺️ Development Roadmap

### ✅ Completed Features
- **Database Integration**: MongoDB with official Go driver
- **Authentication System**: JWT-based auth with role-based access
- **Comprehensive API**: Production-grade Swagger documentation
- **Docker Deployment**: Multi-stage builds with Nginx reverse proxy
- **Audit Logging**: Security and compliance tracking
- **Health Monitoring**: System metrics and database connectivity

### 🚧 In Progress
- **Enhanced Security**: Rate limiting, input validation, CORS configuration
- **Testing Infrastructure**: Unit tests, integration tests, CI/CD pipeline
- **Performance Optimization**: Caching, connection pooling, query optimization

### 📋 Planned Features
- **Message Broker Integration**: Redis/RabbitMQ for scalable job processing
- **Advanced Scraping**: Proxy rotation, CAPTCHA solving, session management
- **Cloud Storage**: AWS S3, Google Cloud Storage integration
- **Export Formats**: CSV, Excel, XML, Parquet export options
- **Monitoring Stack**: Prometheus, Grafana, ELK stack integration
- **Advanced Analytics**: Job performance metrics, success rate analysis

### 🎯 Long-term Goals
- **Horizontal Scaling**: Load balancer support, distributed processing
- **Machine Learning**: Content classification, extraction optimization
- **API Ecosystem**: Webhooks, third-party integrations, plugin system
- **Enterprise Features**: Multi-tenancy, advanced security, compliance tools

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes with proper tests
4. Ensure all tests pass (`go test ./...`)
5. Update documentation as needed
6. Submit a pull request with detailed description

### Development Setup
```bash
# Clone repository
git clone <repository-url>
cd tdwebscraper

# Install dependencies
cd backend && go mod download

# Run tests
go test ./...

# Start development server
go run cmd/main.go
```

## 📄 License

This project is licensed under the MIT License - see the LICENSE file for details.

## 🆘 Support

For support and questions:
- **GitHub Issues**: Create detailed issue with logs and environment details
- **API Documentation**: Interactive Swagger UI at `/docs` endpoint
- **Health Checks**: Monitor system status at `/health` endpoint
- **Logs**: Check application logs with `docker-compose logs -f truedax-scraper`

### Support Checklist
- [ ] Check health endpoint: `curl http://localhost:9000/health`
- [ ] Review application logs for errors
- [ ] Verify environment configuration
- [ ] Test database connectivity
- [ ] Check resource usage and limits

---

**TrueDax Web Scraper** - Professional web scraping made simple, scalable, and production-ready.