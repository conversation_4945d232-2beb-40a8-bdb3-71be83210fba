package main

// @title           TrueDax Web Scraper API
// @version         1.0.0
// @description     A comprehensive, production-ready REST API for web scraping operations with advanced features including async job scheduling, content extraction, user management, and audit logging.
// @description
// @description     ## Features
// @description     - **Async Job Scheduling**: Priority-based job queue with concurrent processing
// @description     - **Comprehensive Web Scraping**: HTTP and headless Chrome (chromedp) support
// @description     - **Content Extraction**: Headings, paragraphs, links, images, tables, forms, and structured data
// @description     - **Authentication & Authorization**: JWT-based stateless auth with role-based access (user/admin)
// @description     - **Audit Logging**: Comprehensive activity tracking and monitoring
// @description     - **Health Monitoring**: System metrics and health checks
// @description
// @description     ## Authentication
// @description     This API uses JWT (JSON Web Token) authentication. To access protected endpoints:
// @description     1. Register a new account using `/auth/register`
// @description     2. Login using `/auth/login` to get access and refresh tokens
// @description     3. Include the access token in the Authorization header: `Bearer <token>`
// @description     4. Use `/auth/refresh` to get new tokens when the access token expires
// @description
// @description     ## Rate Limiting
// @description     API endpoints are rate-limited to ensure fair usage and system stability.
// @description
// @description     ## Error Handling
// @description     The API returns standard HTTP status codes and JSON error responses with descriptive messages.
// @termsOfService  https://truedax.com/terms

// @contact.name   TrueDax Support Team
// @contact.url    https://truedax.com/support
// @contact.email  <EMAIL>

// @license.name  MIT License
// @license.url   https://opensource.org/licenses/MIT

// @host      localhost:9000
// @BasePath  /
// @schemes   http https

// @securityDefinitions.apikey BearerAuth
// @in header
// @name Authorization
// @description Type "Bearer" followed by a space and JWT token.

// @tag.name auth
// @tag.description Authentication and authorization endpoints

// @tag.name jobs
// @tag.description Job management and web scraping operations

// @tag.name admin
// @tag.description Administrative endpoints (admin role required)

// @tag.name health
// @tag.description Health check and system monitoring endpoints

import (
	"log"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/repository/mongodb"
	"go-rest-api/internal/router"
	"go-rest-api/internal/services/monitoring"

	_ "go-rest-api/docs"
)

func main() {
	// Load configuration
	cfg, err := config.Load()
	if err != nil {
		log.Fatalf("Failed to load configuration: %v", err)
	}

	// Initialize logger
	config.Initialize(cfg)

	// Initialize database
	if err := config.InitializeDatabase(cfg); err != nil {
		config.Fatalf("Failed to initialize database: %v", err)
	}

	// Initialize repositories
	repos := mongodb.NewRepositories()

	// Initialize monitoring service and start metrics collection
	monitoringService := monitoring.NewMonitoringService(repos)
	monitoringService.StartMetricsCollection(5 * time.Minute)

	// Log system startup
	monitoringService.LogSystemEvent("info", "startup", "Application starting", map[string]interface{}{
		"version": "1.0.0",
		"port":    cfg.Server.Port,
	})

	// Setup router with dependencies
	r := router.SetupRouter(cfg, repos)

	config.Infof("Server starting on port %s", cfg.Server.Port)
	config.Fatal(r.Run(":" + cfg.Server.Port))
}