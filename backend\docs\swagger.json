{"schemes": ["http", "https"], "swagger": "2.0", "info": {"description": "A comprehensive, production-ready REST API for web scraping operations with advanced features including async job scheduling, content extraction, user management, and audit logging.\n\n## Features\n- **Async Job Scheduling**: Priority-based job queue with concurrent processing\n- **Comprehensive Web Scraping**: HTTP and headless Chrome (chromedp) support\n- **Content Extraction**: Headings, paragraphs, links, images, tables, forms, and structured data\n- **Authentication & Authorization**: JWT-based stateless auth with role-based access (user/admin)\n- **Audit Logging**: Comprehensive activity tracking and monitoring\n- **Health Monitoring**: System metrics and health checks\n\n## Authentication\nThis API uses JWT (JSON Web Token) authentication. To access protected endpoints:\n1. Register a new account using `/auth/register`\n2. Login using `/auth/login` to get access and refresh tokens\n3. Include the access token in the Authorization header: `Bearer <token>`\n4. Use `/auth/refresh` to get new tokens when the access token expires\n\n## Rate Limiting\nAPI endpoints are rate-limited to ensure fair usage and system stability.\n\n## Error Handling\nThe API returns standard HTTP status codes and JSON error responses with descriptive messages.", "title": "TrueDax Web Scraper API", "termsOfService": "https://truedax.com/terms", "contact": {"name": "TrueDax Support Team", "url": "https://truedax.com/support", "email": "<EMAIL>"}, "license": {"name": "MIT License", "url": "https://opensource.org/licenses/MIT"}, "version": "1.0.0"}, "host": "localhost:9000", "basePath": "/", "paths": {"/admin/audit-logs": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a paginated list of audit logs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Get audit logs (Admin only)", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by user ID", "name": "user_id", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handler.AuditLogListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/admin/system-logs": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a paginated list of system logs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Get system logs (Admin only)", "parameters": [{"type": "integer", "default": 1, "description": "Page number", "name": "page", "in": "query"}, {"type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"type": "string", "description": "Filter by log level", "name": "level", "in": "query"}, {"type": "string", "description": "Filter by component", "name": "component", "in": "query"}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handler.SystemLogListResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "403": {"description": "Forbidden", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/admin/users": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a comprehensive, paginated list of all users in the system. This endpoint is restricted to administrators only.\n\n**User Information Included:**\n- Basic profile (email, name, role)\n- Account status (active, locked, disabled)\n- Security metrics (failed login attempts, lock status)\n- Activity timestamps (created, last login)\n\n**Admin Features:**\n- Filter by role, status, or activity\n- Search by email or name\n- Sort by various criteria\n- Export user data for compliance\n\n**Security Note:** Sensitive information like passwords and tokens are never included in responses.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["admin"], "summary": "Get all users (Admin only)", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number (1-based)", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Items per page", "name": "limit", "in": "query"}, {"enum": ["user", "admin"], "type": "string", "description": "Filter by user role", "name": "role", "in": "query"}, {"enum": ["active", "inactive", "locked"], "type": "string", "description": "Filter by account status", "name": "status", "in": "query"}, {"type": "string", "description": "Search by email or name", "name": "search", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved users list with pagination", "schema": {"$ref": "#/definitions/handler.UserListResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "User not authenticated", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "403": {"description": "Access denied - admin role required", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during user retrieval", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/auth/login": {"post": {"description": "Authenticate user with email and password credentials. Returns JWT access and refresh tokens for subsequent API calls.\n\n**Security Features:**\n- Account lockout after multiple failed attempts\n- Password strength validation\n- Device and IP tracking\n- Audit logging of login attempts\n\n**Rate Limiting:** This endpoint is rate-limited to prevent brute force attacks.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "User login", "parameters": [{"description": "User login credentials including email, password, and optional device info", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/auth.LoginRequest"}}], "responses": {"200": {"description": "Successfully authenticated - returns access and refresh tokens", "schema": {"$ref": "#/definitions/auth.TokenPair"}}, "400": {"description": "Invalid request format or missing required fields", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Invalid credentials, account locked, or account disabled", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "429": {"description": "Too many login attempts - rate limit exceeded", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during authentication", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/auth/logout": {"post": {"security": [{"BearerAuth": []}], "description": "Logout the current user and revoke all refresh tokens associated with their account. This invalidates all active sessions.\n\n**Security Features:**\n- Revokes all refresh tokens for the user\n- Invalidates all active sessions across devices\n- Logs the logout event for audit purposes\n\n**Note:** After logout, the user must login again to access protected endpoints. The access token becomes invalid immediately.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "User logout", "responses": {"200": {"description": "Successfully logged out - all sessions invalidated", "schema": {"$ref": "#/definitions/handler.MessageResponse"}}, "401": {"description": "User not authenticated or invalid token", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during logout", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/auth/refresh": {"post": {"description": "Generate a new access token using a valid refresh token. This endpoint should be called when the access token expires.\n\n**Token Lifecycle:**\n- Access tokens expire in 15 minutes (configurable)\n- Refresh tokens expire in 7 days (configurable)\n- Each refresh generates a new access token\n- Refresh tokens are single-use and automatically rotated\n\n**Security Features:**\n- Refresh tokens are hashed and stored securely\n- Device and IP tracking for security monitoring\n- Automatic token rotation prevents replay attacks", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "Refresh access token", "parameters": [{"description": "Refresh token obtained from login or previous refresh", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/auth.RefreshRequest"}}], "responses": {"200": {"description": "Successfully refreshed - returns new access and refresh tokens", "schema": {"$ref": "#/definitions/auth.TokenPair"}}, "400": {"description": "Invalid request format or missing refresh token", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Invalid, expired, or revoked refresh token", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during token refresh", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/auth/register": {"post": {"description": "Create a new user account with email verification and secure password hashing.\n\n**Password Requirements:**\n- Minimum 8 characters\n- Must contain uppercase, lowercase, number, and special character\n\n**Account Features:**\n- Email-based authentication\n- Role-based access control (default: user)\n- Audit logging of account creation\n\n**Note:** All new accounts are created with 'user' role by default. Admin privileges must be granted separately.", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["auth"], "summary": "User registration", "parameters": [{"description": "User registration details including email, password, first name, and last name", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/auth.RegisterRequest"}}], "responses": {"201": {"description": "Successfully created user account - returns user details (password excluded)", "schema": {"$ref": "#/definitions/model.User"}}, "400": {"description": "Invalid request format, missing fields, or password doesn't meet requirements", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "409": {"description": "User with this email already exists", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during registration", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/health": {"get": {"description": "Check if the API is running", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["health"], "summary": "Health check", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/router.HealthResponse"}}}}}, "/jobs": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve a paginated list of the current user's scraping jobs with filtering and sorting options.\n\n**Response includes:**\n- Job summary information (ID, name, status, progress)\n- Creation and completion timestamps\n- Result counts and success rates\n- Pagination metadata\n\n**Job Statuses:**\n- `pending`: Job is queued and waiting to start\n- `running`: Job is currently being processed\n- `completed`: Job finished successfully\n- `failed`: Job encountered an error\n- `cancelled`: Job was cancelled by user or admin", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Get user's scraping jobs", "parameters": [{"minimum": 1, "type": "integer", "default": 1, "description": "Page number (1-based)", "name": "page", "in": "query"}, {"maximum": 100, "minimum": 1, "type": "integer", "default": 10, "description": "Number of items per page", "name": "limit", "in": "query"}, {"enum": ["pending", "running", "completed", "failed", "cancelled"], "type": "string", "description": "Filter by job status", "name": "status", "in": "query"}, {"enum": ["created_at", "-created_at", "name", "-name", "status", "-status"], "type": "string", "default": "-created_at", "description": "Sort order", "name": "sort", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved jobs list with pagination", "schema": {"$ref": "#/definitions/handler.JobListResponse"}}, "400": {"description": "Invalid query parameters", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "User not authenticated or invalid token", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during job retrieval", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}, "post": {"security": [{"BearerAuth": []}], "description": "Create a new asynchronous web scraping job with comprehensive configuration options. Jobs are queued and processed based on priority.\n\n**Scraping Capabilities:**\n- HTTP client for static content (fast)\n- Headless Chrome for JavaScript-heavy sites\n- Custom selectors for targeted data extraction\n- Configurable delays and timeouts\n- User-agent and header customization\n\n**Content Extraction:**\n- Headings (H1-H6), paragraphs, links, images\n- Tables, forms, lists, structured data\n- Custom CSS selector-based extraction\n\n**Job Management:**\n- Priority-based queue processing\n- Progress tracking and status updates\n- Comprehensive result storage in database", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Create a new web scraping job", "parameters": [{"description": "Job configuration including URL, selectors, and scraping options", "name": "job", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.CreateJobRequest"}}], "responses": {"201": {"description": "Successfully created job - returns job details with unique ID", "schema": {"$ref": "#/definitions/model.Job"}}, "400": {"description": "Invalid request format, missing required fields, or invalid configuration", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "User not authenticated or invalid token", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during job creation", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/jobs/stats": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve statistics about user's scraping jobs", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Get job statistics", "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/model.JobStats"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal Server Error", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/jobs/test-scrape": {"post": {"security": [{"BearerAuth": []}], "description": "Test scraping functionality on a single URL without creating a job", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Test scrape a single URL", "parameters": [{"description": "URL and selectors to test", "name": "request", "in": "body", "required": true, "schema": {"$ref": "#/definitions/handler.TestScrapeRequest"}}], "responses": {"200": {"description": "Scraping test results", "schema": {"type": "object"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/jobs/{id}": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve detailed information about a specific scraping job", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Get a specific job", "parameters": [{"type": "string", "description": "Job ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/model.Job"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}, "put": {"security": [{"BearerAuth": []}], "description": "Update configuration of a pending scraping job", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Update a job", "parameters": [{"type": "string", "description": "Job ID", "name": "id", "in": "path", "required": true}, {"description": "Updated job configuration", "name": "job", "in": "body", "required": true, "schema": {"$ref": "#/definitions/model.UpdateJobRequest"}}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/model.Job"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}, "delete": {"security": [{"BearerAuth": []}], "description": "Delete a completed, failed, or cancelled scraping job", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Delete a job", "parameters": [{"type": "string", "description": "Job ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handler.MessageResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/jobs/{id}/cancel": {"post": {"security": [{"BearerAuth": []}], "description": "Cancel a pending, queued, or running scraping job", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Cancel a job", "parameters": [{"type": "string", "description": "Job ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handler.MessageResponse"}}, "400": {"description": "Bad Request", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/jobs/{id}/queue-position": {"get": {"security": [{"BearerAuth": []}], "description": "Get the current position of a job in the execution queue", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Get job queue position", "parameters": [{"type": "string", "description": "Job ID", "name": "id", "in": "path", "required": true}], "responses": {"200": {"description": "OK", "schema": {"$ref": "#/definitions/handler.QueuePositionResponse"}}, "401": {"description": "Unauthorized", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "404": {"description": "Not Found", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}, "/jobs/{id}/results": {"get": {"security": [{"BearerAuth": []}], "description": "Retrieve comprehensive results from a completed scraping job with pagination support for large datasets.\n\n**Result Structure:**\n- **Headings**: H1-H6 tags with text and hierarchy\n- **Paragraphs**: All paragraph text content\n- **Links**: URLs, anchor text, titles, and targets\n- **Images**: URLs, alt text, dimensions\n- **Tables**: Headers and row data\n- **Forms**: Field types, names, and validation rules\n- **Lists**: Ordered and unordered list items\n- **Structured Data**: JSON-LD, microdata, RDFa\n- **Custom Selectors**: User-defined CSS selector results\n\n**Metadata Included:**\n- Page load times and performance metrics\n- JavaScript execution status\n- HTTP response headers and status codes\n- Error messages and retry information", "consumes": ["application/json"], "produces": ["application/json"], "tags": ["jobs"], "summary": "Get job scraping results", "parameters": [{"type": "string", "description": "Job ID (unique identifier)", "name": "id", "in": "path", "required": true}, {"minimum": 1, "type": "integer", "default": 1, "description": "Page number for paginated results", "name": "page", "in": "query"}, {"maximum": 1000, "minimum": 1, "type": "integer", "default": 50, "description": "Number of results per page", "name": "limit", "in": "query"}, {"enum": ["json", "csv", "xlsx"], "type": "string", "default": "json", "description": "Response format", "name": "format", "in": "query"}], "responses": {"200": {"description": "Successfully retrieved job results with extracted content", "schema": {"$ref": "#/definitions/model.ScrapingResult"}}, "400": {"description": "Invalid job ID or query parameters", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "401": {"description": "User not authenticated or no access to this job", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "404": {"description": "Job not found or no results available", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}, "500": {"description": "Internal server error during result retrieval", "schema": {"$ref": "#/definitions/handler.ErrorResponse"}}}}}}, "definitions": {"auth.LoginRequest": {"type": "object", "required": ["email", "password"], "properties": {"device_info": {"type": "string"}, "email": {"type": "string"}, "password": {"type": "string", "minLength": 6}}}, "auth.RefreshRequest": {"type": "object", "required": ["refresh_token"], "properties": {"refresh_token": {"type": "string"}}}, "auth.RegisterRequest": {"type": "object", "required": ["email", "first_name", "last_name", "password"], "properties": {"email": {"type": "string"}, "first_name": {"type": "string"}, "last_name": {"type": "string"}, "password": {"type": "string", "minLength": 8}}}, "auth.TokenPair": {"type": "object", "properties": {"access_token": {"type": "string"}, "expires_in": {"type": "integer"}, "refresh_token": {"type": "string"}, "token_type": {"type": "string"}}}, "handler.AuditLogListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "logs": {"type": "array", "items": {"$ref": "#/definitions/handler.AuditLogResponse"}}, "page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "handler.AuditLogResponse": {"type": "object", "properties": {"action": {"type": "string"}, "created_at": {"type": "string"}, "details": {"type": "string"}, "id": {"type": "string"}, "ip_address": {"type": "string"}, "resource_id": {"type": "string"}, "resource_type": {"type": "string"}, "user_agent": {"type": "string"}, "user_email": {"type": "string"}, "user_id": {"type": "string"}}}, "handler.ErrorResponse": {"type": "object", "properties": {"error": {"type": "string", "example": "Invalid credentials"}}}, "handler.JobListResponse": {"type": "object", "properties": {"jobs": {"type": "array", "items": {"$ref": "#/definitions/model.JobSummary"}}, "limit": {"type": "integer", "example": 10}, "page": {"type": "integer", "example": 1}, "total": {"type": "integer", "example": 150}, "total_pages": {"type": "integer", "example": 15}}}, "handler.MessageResponse": {"type": "object", "properties": {"message": {"type": "string", "example": "Operation completed successfully"}}}, "handler.QueuePositionResponse": {"type": "object", "properties": {"job_id": {"type": "string", "example": "job_123456"}, "position": {"type": "integer", "example": 3}}}, "handler.SystemLogListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "logs": {"type": "array", "items": {"$ref": "#/definitions/handler.SystemLogResponse"}}, "page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}}}, "handler.SystemLogResponse": {"type": "object", "properties": {"component": {"type": "string"}, "created_at": {"type": "string"}, "details": {"type": "string"}, "id": {"type": "string"}, "level": {"type": "string"}, "message": {"type": "string"}}}, "handler.TestScrapeRequest": {"type": "object", "required": ["url"], "properties": {"comprehensive_mode": {"description": "Extract all content automatically", "type": "boolean", "example": true}, "javascript_enabled": {"type": "boolean", "example": false}, "selectors": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"\"content\"": "\"p\"}", "{\"title\"": "\"h1\""}}, "url": {"type": "string", "example": "https://example.com"}}}, "handler.UserListResponse": {"type": "object", "properties": {"limit": {"type": "integer"}, "page": {"type": "integer"}, "total": {"type": "integer"}, "total_pages": {"type": "integer"}, "users": {"type": "array", "items": {"$ref": "#/definitions/handler.UserResponse"}}}}, "handler.UserResponse": {"type": "object", "properties": {"created_at": {"type": "string"}, "email": {"type": "string"}, "failed_login_attempts": {"type": "integer"}, "first_name": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "last_login": {"type": "string"}, "last_name": {"type": "string"}, "locked_until": {"type": "string"}, "role": {"type": "string"}}}, "model.CreateJobRequest": {"type": "object", "required": ["config", "name"], "properties": {"config": {"$ref": "#/definitions/model.ScrapingConfig"}, "description": {"type": "string", "example": "Scrape product data"}, "name": {"type": "string", "example": "Product Scraper"}, "priority": {"allOf": [{"$ref": "#/definitions/model.JobPriority"}], "example": 2}}}, "model.FieldData": {"type": "object", "properties": {"name": {"type": "string"}, "placeholder": {"type": "string"}, "required": {"type": "boolean"}, "type": {"type": "string"}, "value": {"type": "string"}}}, "model.FormData": {"type": "object", "properties": {"action": {"type": "string"}, "fields": {"type": "array", "items": {"$ref": "#/definitions/model.FieldData"}}, "method": {"type": "string"}}}, "model.HeadingData": {"type": "object", "properties": {"level": {"type": "integer"}, "text": {"type": "string"}}}, "model.ImageData": {"type": "object", "properties": {"alt": {"type": "string"}, "height": {"type": "string"}, "title": {"type": "string"}, "url": {"type": "string"}, "width": {"type": "string"}}}, "model.Job": {"type": "object", "required": ["name"], "properties": {"completed_at": {"type": "string", "example": "2023-07-18T11:00:00Z"}, "config": {"$ref": "#/definitions/model.ScrapingConfig"}, "created_at": {"type": "string", "example": "2023-07-18T10:30:00Z"}, "description": {"type": "string", "example": "Scrape product information from e-commerce site"}, "error_msg": {"type": "string", "example": "Connection timeout"}, "id": {"type": "string"}, "job_id": {"type": "string", "example": "job_123456"}, "name": {"type": "string", "example": "E-commerce Product Scraper"}, "priority": {"allOf": [{"$ref": "#/definitions/model.JobPriority"}], "example": 2}, "progress": {"type": "integer", "example": 75}, "result_count": {"type": "integer", "example": 150}, "result_url": {"type": "string", "example": "https://storage.example.com/results/job_123456.json"}, "started_at": {"type": "string", "example": "2023-07-18T10:35:00Z"}, "status": {"allOf": [{"$ref": "#/definitions/model.JobStatus"}], "example": "pending"}, "success_rate": {"type": "number", "example": 95.5}, "total_load_time_ms": {"type": "integer", "example": 45000}, "updated_at": {"type": "string", "example": "2023-07-18T10:30:00Z"}, "user_email": {"description": "Computed field", "type": "string", "example": "<EMAIL>"}, "user_id": {"type": "string"}}}, "model.JobPriority": {"type": "integer", "enum": [1, 2, 3, 4], "x-enum-varnames": ["PriorityLow", "PriorityNormal", "PriorityHigh", "PriorityCritical"]}, "model.JobStats": {"type": "object", "properties": {"completed_jobs": {"type": "integer", "example": 1200}, "failed_jobs": {"type": "integer", "example": 42}, "pending_jobs": {"type": "integer", "example": 5}, "running_jobs": {"type": "integer", "example": 3}, "total_jobs": {"type": "integer", "example": 1250}}}, "model.JobStatus": {"type": "string", "enum": ["pending", "queued", "running", "completed", "failed", "cancelled"], "x-enum-varnames": ["JobStatusPending", "JobStatusQueued", "JobStatusRunning", "JobStatusCompleted", "JobStatusFailed", "JobStatusCancelled"]}, "model.JobSummary": {"type": "object", "properties": {"created_at": {"type": "string", "example": "2023-07-18T10:30:00Z"}, "id": {"type": "string", "example": "job_123456"}, "name": {"type": "string", "example": "Product Scraper"}, "priority": {"allOf": [{"$ref": "#/definitions/model.JobPriority"}], "example": 2}, "progress": {"type": "integer", "example": 75}, "result_count": {"type": "integer", "example": 150}, "status": {"allOf": [{"$ref": "#/definitions/model.JobStatus"}], "example": "running"}}}, "model.LinkData": {"type": "object", "properties": {"target": {"type": "string"}, "text": {"type": "string"}, "title": {"type": "string"}, "url": {"type": "string"}}}, "model.ListData": {"type": "object", "properties": {"items": {"type": "array", "items": {"type": "string"}}, "type": {"description": "\"ordered\" or \"unordered\"", "type": "string"}}}, "model.ScrapingConfig": {"type": "object", "required": ["url"], "properties": {"delay_ms": {"type": "integer", "example": 1000}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "javascript_enabled": {"type": "boolean", "example": false}, "max_pages": {"type": "integer", "example": 10}, "selectors": {"type": "object", "additionalProperties": {"type": "string"}, "example": {"\"price\"": "\".price\"}", "{\"title\"": "\"h1\""}}, "timeout": {"type": "integer", "example": 30}, "url": {"type": "string", "example": "https://example.com"}, "user_agent": {"type": "string", "example": "Mozilla/5.0..."}}}, "model.ScrapingContent": {"type": "object", "properties": {"all_text": {"type": "string"}, "forms": {"type": "array", "items": {"$ref": "#/definitions/model.FormData"}}, "headings": {"type": "array", "items": {"$ref": "#/definitions/model.HeadingData"}}, "images": {"type": "array", "items": {"$ref": "#/definitions/model.ImageData"}}, "links": {"type": "array", "items": {"$ref": "#/definitions/model.LinkData"}}, "lists": {"type": "array", "items": {"$ref": "#/definitions/model.ListData"}}, "main_content": {"type": "string"}, "paragraphs": {"type": "array", "items": {"type": "string"}}, "structured_data": {}, "tables": {"type": "array", "items": {"$ref": "#/definitions/model.TableData"}}}}, "model.ScrapingMetadata": {"type": "object", "properties": {"charset": {"type": "string"}, "content_length": {"type": "integer"}, "content_type": {"type": "string"}, "headers": {"type": "object", "additionalProperties": {"type": "string"}}, "javascript_used": {"type": "boolean"}, "language": {"type": "string"}, "last_modified": {"type": "string"}, "response_time_ms": {"type": "integer"}, "user_agent": {"type": "string"}}}, "model.ScrapingResult": {"type": "object", "properties": {"content": {"description": "Comprehensive content extraction", "allOf": [{"$ref": "#/definitions/model.ScrapingContent"}]}, "description": {"type": "string"}, "error_msg": {"type": "string"}, "id": {"type": "string"}, "job_id": {"type": "string"}, "metadata": {"$ref": "#/definitions/model.ScrapingMetadata"}, "scraped_at": {"type": "string"}, "status_code": {"type": "integer"}, "success": {"type": "boolean"}, "title": {"type": "string"}, "url": {"type": "string"}, "uuid": {"type": "string"}}}, "model.TableData": {"type": "object", "properties": {"headers": {"type": "array", "items": {"type": "string"}}, "rows": {"type": "array", "items": {"type": "array", "items": {"type": "string"}}}}}, "model.UpdateJobRequest": {"type": "object", "properties": {"config": {"$ref": "#/definitions/model.ScrapingConfig"}, "description": {"type": "string", "example": "Updated description"}, "name": {"type": "string", "example": "Updated Product Scraper"}, "priority": {"allOf": [{"$ref": "#/definitions/model.JobPriority"}], "example": 3}}}, "model.User": {"type": "object", "required": ["email"], "properties": {"api_key": {"type": "string"}, "created_at": {"type": "string"}, "email": {"type": "string"}, "email_verified": {"type": "boolean"}, "first_name": {"type": "string"}, "id": {"type": "string"}, "is_active": {"type": "boolean"}, "last_login": {"type": "string"}, "last_name": {"type": "string"}, "role": {"$ref": "#/definitions/model.UserRole"}, "updated_at": {"type": "string"}, "uuid": {"type": "string"}}}, "model.UserRole": {"type": "string", "enum": ["user", "admin"], "x-enum-varnames": ["UserRoleUser", "UserRoleAdmin"]}, "monitoring.CheckResult": {"type": "object", "properties": {"latency": {"type": "string", "example": "5ms"}, "message": {"type": "string", "example": "Database connection successful"}, "status": {"type": "string", "example": "healthy"}}}, "monitoring.HealthStatus": {"type": "object", "properties": {"checks": {"type": "object", "additionalProperties": {"$ref": "#/definitions/monitoring.CheckResult"}}, "metrics": {"$ref": "#/definitions/monitoring.SystemMetrics"}, "status": {"type": "string", "example": "healthy"}, "timestamp": {"type": "string", "example": "2024-07-29T15:44:02Z"}, "uptime": {"type": "string", "example": "2h30m45s"}, "version": {"type": "string", "example": "1.0.0"}}}, "monitoring.SystemMetrics": {"type": "object", "properties": {"cpu_usage": {"type": "number"}, "database_connections": {"type": "integer"}, "gc_pause_total_ns": {"type": "integer"}, "goroutine_count": {"type": "integer"}, "heap_size_bytes": {"type": "integer"}, "heap_size_mb": {"type": "number"}, "memory_usage_bytes": {"type": "integer"}, "memory_usage_mb": {"type": "number"}, "timestamp": {"type": "string"}}}, "router.HealthResponse": {"type": "object", "properties": {"status": {"type": "string", "example": "ok"}}}}, "securityDefinitions": {"BearerAuth": {"description": "Type \"Bearer\" followed by a space and JWT token.", "type": "<PERSON><PERSON><PERSON><PERSON>", "name": "Authorization", "in": "header"}}, "tags": [{"description": "Authentication and authorization endpoints", "name": "auth"}, {"description": "Job management and web scraping operations", "name": "jobs"}, {"description": "Administrative endpoints (admin role required)", "name": "admin"}, {"description": "Health check and system monitoring endpoints", "name": "health"}]}