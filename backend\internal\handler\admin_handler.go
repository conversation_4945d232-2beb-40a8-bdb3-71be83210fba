package handler

import (
	"net/http"
	"strconv"
	"time"

	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AdminHandler handles admin-specific operations
type AdminHandler struct {
	repos *repository.Repositories
}

// NewAdminHandler creates a new AdminHandler
func NewAdminHandler(repos *repository.Repositories) *AdminHandler {
	return &AdminHandler{
		repos: repos,
	}
}

// UserListResponse represents a paginated list of users
type UserListResponse struct {
	Users      []UserResponse `json:"users"`
	Total      int64          `json:"total"`
	Page       int            `json:"page"`
	Limit      int            `json:"limit"`
	TotalPages int            `json:"total_pages"`
}

// UserResponse represents a user in API responses (without sensitive data)
type UserResponse struct {
	ID                   uuid.UUID `json:"id"`
	Email                string    `json:"email"`
	FirstName            string    `json:"first_name"`
	LastName             string    `json:"last_name"`
	Role                 string    `json:"role"`
	IsActive             bool      `json:"is_active"`
	CreatedAt            string    `json:"created_at"`
	LastLogin            *string   `json:"last_login,omitempty"`
	FailedLoginAttempts  int       `json:"failed_login_attempts"`
	LockedUntil          *string   `json:"locked_until,omitempty"`
}

// AuditLogListResponse represents a paginated list of audit logs
type AuditLogListResponse struct {
	Logs       []AuditLogResponse `json:"logs"`
	Total      int64              `json:"total"`
	Page       int                `json:"page"`
	Limit      int                `json:"limit"`
	TotalPages int                `json:"total_pages"`
}

// AuditLogResponse represents an audit log in API responses
type AuditLogResponse struct {
	ID           uuid.UUID `json:"id"`
	UserID       uuid.UUID `json:"user_id"`
	UserEmail    string    `json:"user_email"`
	Action       string    `json:"action"`
	ResourceType string    `json:"resource_type"`
	ResourceID   string    `json:"resource_id,omitempty"`
	IPAddress    string    `json:"ip_address"`
	UserAgent    string    `json:"user_agent"`
	Details      string    `json:"details,omitempty"`
	CreatedAt    string    `json:"created_at"`
}

// SystemLogListResponse represents a paginated list of system logs
type SystemLogListResponse struct {
	Logs       []SystemLogResponse `json:"logs"`
	Total      int64               `json:"total"`
	Page       int                 `json:"page"`
	Limit      int                 `json:"limit"`
	TotalPages int                 `json:"total_pages"`
}

// SystemLogResponse represents a system log in API responses
type SystemLogResponse struct {
	ID        uuid.UUID `json:"id"`
	Level     string    `json:"level"`
	Component string    `json:"component"`
	Message   string    `json:"message"`
	Details   string    `json:"details,omitempty"`
	CreatedAt string    `json:"created_at"`
}

// GetUsers godoc
// @Summary      Get all users (Admin only)
// @Description  Retrieve a comprehensive, paginated list of all users in the system. This endpoint is restricted to administrators only.
// @Description
// @Description  **User Information Included:**
// @Description  - Basic profile (email, name, role)
// @Description  - Account status (active, locked, disabled)
// @Description  - Security metrics (failed login attempts, lock status)
// @Description  - Activity timestamps (created, last login)
// @Description
// @Description  **Admin Features:**
// @Description  - Filter by role, status, or activity
// @Description  - Search by email or name
// @Description  - Sort by various criteria
// @Description  - Export user data for compliance
// @Description
// @Description  **Security Note:** Sensitive information like passwords and tokens are never included in responses.
// @Tags         admin
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page query int false "Page number (1-based)" default(1) minimum(1)
// @Param        limit query int false "Items per page" default(10) minimum(1) maximum(100)
// @Param        role query string false "Filter by user role" Enums(user,admin)
// @Param        status query string false "Filter by account status" Enums(active,inactive,locked)
// @Param        search query string false "Search by email or name"
// @Success      200 {object} UserListResponse "Successfully retrieved users list with pagination"
// @Failure      400 {object} ErrorResponse "Invalid query parameters"
// @Failure      401 {object} ErrorResponse "User not authenticated"
// @Failure      403 {object} ErrorResponse "Access denied - admin role required"
// @Failure      500 {object} ErrorResponse "Internal server error during user retrieval"
// @Router       /admin/users [get]
func (h *AdminHandler) GetUsers(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	offset := (page - 1) * limit

	// Get users
	users, err := h.repos.User.List(c.Request.Context(), limit, offset)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to retrieve users"})
		return
	}

	// Get total count
	total, err := h.repos.User.Count(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to count users"})
		return
	}

	// Convert to response format
	userResponses := make([]UserResponse, len(users))
	for i, user := range users {
		userResponses[i] = UserResponse{
			ID:                  user.UUID,
			Email:               user.Email,
			FirstName:           user.FirstName,
			LastName:            user.LastName,
			Role:                string(user.Role),
			IsActive:            user.IsActive,
			CreatedAt:           user.CreatedAt.Format("2006-01-02T15:04:05Z"),
			FailedLoginAttempts: user.FailedLoginAttempts,
		}
		
		if user.LastLogin != nil {
			lastLogin := user.LastLogin.Format("2006-01-02T15:04:05Z")
			userResponses[i].LastLogin = &lastLogin
		}
		
		if user.LockedUntil != nil {
			lockedUntil := user.LockedUntil.Format("2006-01-02T15:04:05Z")
			userResponses[i].LockedUntil = &lockedUntil
		}
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	response := UserListResponse{
		Users:      userResponses,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

// GetAuditLogs godoc
// @Summary      Get audit logs (Admin only)
// @Description  Retrieve a paginated list of audit logs
// @Tags         admin
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page query int false "Page number" default(1)
// @Param        limit query int false "Items per page" default(10)
// @Param        user_id query string false "Filter by user ID"
// @Success      200 {object} AuditLogListResponse
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      403 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /admin/audit-logs [get]
func (h *AdminHandler) GetAuditLogs(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	userIDStr := c.Query("user_id")
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	offset := (page - 1) * limit

	var logs []*model.AuditLog
	var total int64
	var err error

	// Filter by user ID if provided
	if userIDStr != "" {
		userID, err := uuid.Parse(userIDStr)
		if err != nil {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid user ID format"})
			return
		}
		logs, err = h.repos.Audit.GetAuditLogsByUserID(c.Request.Context(), userID, limit, offset)
	} else {
		// For now, get logs by date range (last 30 days) since we don't have a List method
		endDate := time.Now()
		startDate := endDate.AddDate(0, 0, -30)
		logs, err = h.repos.Audit.GetAuditLogsByDateRange(c.Request.Context(), startDate, endDate, limit, offset)
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to retrieve audit logs"})
		return
	}

	// Get total count
	total, err = h.repos.Audit.CountAuditLogs(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to count audit logs"})
		return
	}

	// Convert to response format
	logResponses := make([]AuditLogResponse, len(logs))
	for i, log := range logs {
		var userID uuid.UUID

		if log.UserID != nil {
			userID = *log.UserID
		}

		logResponses[i] = AuditLogResponse{
			ID:           log.UUID,
			UserID:       userID,
			UserEmail:    "", // We'll need to fetch this separately if needed
			Action:       log.Action,
			ResourceType: log.ResourceType,
			ResourceID:   log.ResourceID,
			IPAddress:    log.IPAddress,
			UserAgent:    log.UserAgent,
			CreatedAt:    log.CreatedAt.Format("2006-01-02T15:04:05Z"),
		}

		if log.Details != nil {
			if detailsStr, ok := log.Details.(string); ok {
				logResponses[i].Details = detailsStr
			}
		}
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	response := AuditLogListResponse{
		Logs:       logResponses,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}

// GetSystemLogs godoc
// @Summary      Get system logs (Admin only)
// @Description  Retrieve a paginated list of system logs
// @Tags         admin
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page query int false "Page number" default(1)
// @Param        limit query int false "Items per page" default(10)
// @Param        level query string false "Filter by log level"
// @Param        component query string false "Filter by component"
// @Success      200 {object} SystemLogListResponse
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      403 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /admin/system-logs [get]
func (h *AdminHandler) GetSystemLogs(c *gin.Context) {
	// Parse pagination parameters
	page, _ := strconv.Atoi(c.DefaultQuery("page", "1"))
	limit, _ := strconv.Atoi(c.DefaultQuery("limit", "10"))
	level := c.Query("level")
	component := c.Query("component")
	
	if page < 1 {
		page = 1
	}
	if limit < 1 || limit > 100 {
		limit = 10
	}
	
	offset := (page - 1) * limit

	var logs []*model.SystemLog
	var total int64
	var err error

	// Filter by level or component if provided
	if level != "" {
		logs, err = h.repos.Audit.GetSystemLogsByLevel(c.Request.Context(), level, limit, offset)
	} else if component != "" {
		logs, err = h.repos.Audit.GetSystemLogsByComponent(c.Request.Context(), component, limit, offset)
	} else {
		// For now, return empty logs since we don't have a general list method
		logs = []*model.SystemLog{}
		err = nil
	}

	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to retrieve system logs"})
		return
	}

	// Get total count
	total, err = h.repos.Audit.CountSystemLogs(c.Request.Context())
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Failed to count system logs"})
		return
	}

	// Convert to response format
	logResponses := make([]SystemLogResponse, len(logs))
	for i, log := range logs {
		logResponses[i] = SystemLogResponse{
			ID:        log.UUID,
			Level:     log.Level,
			Component: log.Component,
			Message:   log.Message,
			CreatedAt: log.CreatedAt.Format("2006-01-02T15:04:05Z"),
		}

		if log.Details != nil {
			if detailsStr, ok := log.Details.(string); ok {
				logResponses[i].Details = detailsStr
			}
		}
	}

	totalPages := int((total + int64(limit) - 1) / int64(limit))

	response := SystemLogListResponse{
		Logs:       logResponses,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: totalPages,
	}

	c.JSON(http.StatusOK, response)
}
