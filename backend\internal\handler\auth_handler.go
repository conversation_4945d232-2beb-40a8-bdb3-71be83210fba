package handler

import (
	"net/http"
	"strings"

	"go-rest-api/internal/services/auth"
	"go-rest-api/config"
	"go-rest-api/internal/repository"

	"github.com/gin-gonic/gin"
	"github.com/google/uuid"
)

// AuthHandler handles authentication-related requests
type AuthHandler struct {
	authService *auth.AuthService
}

// NewAuthHandler creates a new AuthHandler
func NewAuthHandler(config *config.Config, repos *repository.Repositories) *AuthHandler {
	return &AuthHandler{
		authService: auth.NewAuthService(config, repos),
	}
}

// ErrorResponse represents an error response
type ErrorResponse struct {
	Error string `json:"error" example:"Invalid credentials"`
}



// Login godoc
// @Summary      User login
// @Description  Authenticate user with email and password credentials. Returns JWT access and refresh tokens for subsequent API calls.
// @Description
// @Description  **Security Features:**
// @Description  - Account lockout after multiple failed attempts
// @Description  - Password strength validation
// @Description  - Device and IP tracking
// @Description  - Audit logging of login attempts
// @Description
// @Description  **Rate Limiting:** This endpoint is rate-limited to prevent brute force attacks.
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body auth.LoginRequest true "User login credentials including email, password, and optional device info"
// @Success      200 {object} auth.TokenPair "Successfully authenticated - returns access and refresh tokens"
// @Failure      400 {object} ErrorResponse "Invalid request format or missing required fields"
// @Failure      401 {object} ErrorResponse "Invalid credentials, account locked, or account disabled"
// @Failure      429 {object} ErrorResponse "Too many login attempts - rate limit exceeded"
// @Failure      500 {object} ErrorResponse "Internal server error during authentication"
// @Router       /auth/login [post]
func (h *AuthHandler) Login(c *gin.Context) {
	var req auth.LoginRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request format"})
		return
	}

	// Get client IP
	ipAddress := c.ClientIP()

	// Authenticate user
	tokenPair, err := h.authService.Login(req, ipAddress)
	if err != nil {
		if strings.Contains(err.Error(), "invalid credentials") ||
		   strings.Contains(err.Error(), "account is locked") ||
		   strings.Contains(err.Error(), "account is disabled") {
			c.JSON(http.StatusUnauthorized, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Authentication failed"})
		return
	}
c.JSON(http.StatusOK, tokenPair)
}

// Register godoc
// @Summary      User registration
// @Description  Create a new user account with email verification and secure password hashing.
// @Description
// @Description  **Password Requirements:**
// @Description  - Minimum 8 characters
// @Description  - Must contain uppercase, lowercase, number, and special character
// @Description
// @Description  **Account Features:**
// @Description  - Email-based authentication
// @Description  - Role-based access control (default: user)
// @Description  - Audit logging of account creation
// @Description
// @Description  **Note:** All new accounts are created with 'user' role by default. Admin privileges must be granted separately.
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body auth.RegisterRequest true "User registration details including email, password, first name, and last name"
// @Success      201 {object} model.User "Successfully created user account - returns user details (password excluded)"
// @Failure      400 {object} ErrorResponse "Invalid request format, missing fields, or password doesn't meet requirements"
// @Failure      409 {object} ErrorResponse "User with this email already exists"
// @Failure      500 {object} ErrorResponse "Internal server error during registration"
// @Router       /auth/register [post]
func (h *AuthHandler) Register(c *gin.Context) {
	var req auth.RegisterRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request format"})
		return
	}

	// Register user
	user, err := h.authService.Register(req)
	if err != nil {
		if strings.Contains(err.Error(), "already exists") {
			c.JSON(http.StatusConflict, ErrorResponse{Error: err.Error()})
			return
		}
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Registration failed"})
		return
	}

	c.JSON(http.StatusCreated, user)
}

// RefreshToken godoc
// @Summary      Refresh access token
// @Description  Generate a new access token using a valid refresh token. This endpoint should be called when the access token expires.
// @Description
// @Description  **Token Lifecycle:**
// @Description  - Access tokens expire in 15 minutes (configurable)
// @Description  - Refresh tokens expire in 7 days (configurable)
// @Description  - Each refresh generates a new access token
// @Description  - Refresh tokens are single-use and automatically rotated
// @Description
// @Description  **Security Features:**
// @Description  - Refresh tokens are hashed and stored securely
// @Description  - Device and IP tracking for security monitoring
// @Description  - Automatic token rotation prevents replay attacks
// @Tags         auth
// @Accept       json
// @Produce      json
// @Param        request body auth.RefreshRequest true "Refresh token obtained from login or previous refresh"
// @Success      200 {object} auth.TokenPair "Successfully refreshed - returns new access and refresh tokens"
// @Failure      400 {object} ErrorResponse "Invalid request format or missing refresh token"
// @Failure      401 {object} ErrorResponse "Invalid, expired, or revoked refresh token"
// @Failure      500 {object} ErrorResponse "Internal server error during token refresh"
// @Router       /auth/refresh [post]
func (h *AuthHandler) RefreshToken(c *gin.Context) {
	var req auth.RefreshRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: "Invalid request format"})
		return
	}

	// Get client IP
	ipAddress := c.ClientIP()

	// Refresh token
	tokenPair, err := h.authService.RefreshToken(req, ipAddress)
	if err != nil {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "Invalid or expired refresh token"})
		return
	}

	c.JSON(http.StatusOK, tokenPair)
}

// Logout godoc
// @Summary      User logout
// @Description  Logout the current user and revoke all refresh tokens associated with their account. This invalidates all active sessions.
// @Description
// @Description  **Security Features:**
// @Description  - Revokes all refresh tokens for the user
// @Description  - Invalidates all active sessions across devices
// @Description  - Logs the logout event for audit purposes
// @Description
// @Description  **Note:** After logout, the user must login again to access protected endpoints. The access token becomes invalid immediately.
// @Tags         auth
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200 {object} MessageResponse "Successfully logged out - all sessions invalidated"
// @Failure      401 {object} ErrorResponse "User not authenticated or invalid token"
// @Failure      500 {object} ErrorResponse "Internal server error during logout"
// @Router       /auth/logout [post]
func (h *AuthHandler) Logout(c *gin.Context) {
	// Get user ID from context (set by middleware)
	userID, exists := c.Get("user_id")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	// Logout user
	if err := h.authService.Logout(userID.(uuid.UUID)); err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: "Logout failed"})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{Message: "Logged out successfully"})
}

// Legacy Login function for backward compatibility
// This will be removed in future versions
func Login(c *gin.Context) {
	// For now, return an error directing users to the new endpoint
	c.JSON(http.StatusGone, ErrorResponse{
		Error: "This endpoint is deprecated. Please use /auth/login instead",
	})
}