package handler

import (
	"net/http"

	"go-rest-api/internal/services/monitoring"

	"github.com/gin-gonic/gin"
)

// HealthHandler handles health check endpoints
type HealthHandler struct {
	monitoringService *monitoring.MonitoringService
}

// NewHealthHandler creates a new HealthHandler
func NewHealthHandler(monitoringService *monitoring.MonitoringService) *HealthHandler {
	return &HealthHandler{
		monitoringService: monitoringService,
	}
}

// HealthCheck godoc
// @Summary      Application health check
// @Description  Comprehensive health check endpoint that monitors all critical system components and returns detailed status information.
// @Description
// @Description  **Health Checks Include:**
// @Description  - **Database Connectivity**: MongoDB connection and query performance
// @Description  - **Memory Usage**: Current memory consumption and limits
// @Description  - **System Metrics**: CPU usage, disk space, network status
// @Description  - **Service Dependencies**: External service availability
// @Description
// @Description  **Response Status Codes:**
// @Description  - `200 OK`: All systems healthy and operational
// @Description  - `503 Service Unavailable`: One or more critical components unhealthy
// @Description
// @Description  **Monitoring Integration:** This endpoint is designed for use with monitoring systems like Prometheus, Grafana, or load balancers for health checks.
// @Tags         health
// @Accept       json
// @Produce      json
// @Success      200 {object} monitoring.HealthStatus "All systems healthy - detailed status information"
// @Failure      503 {object} monitoring.HealthStatus "System unhealthy - detailed error information"
// @Router       /health [get]
func (h *HealthHandler) HealthCheck(c *gin.Context) {
	healthStatus := h.monitoringService.GetHealthStatus()

	statusCode := http.StatusOK
	if healthStatus.Status != "healthy" {
		statusCode = http.StatusServiceUnavailable
	}

	c.JSON(statusCode, healthStatus)
}


