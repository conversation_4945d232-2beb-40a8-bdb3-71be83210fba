package handler

import (
	"net/http"
	"strconv"

	"github.com/gin-gonic/gin"
	"go-rest-api/internal/model"
	"go-rest-api/internal/services/job"
)

// JobHandler handles HTTP requests for job management
type Job<PERSON><PERSON><PERSON> struct {
	jobService *job.JobService
}

// NewJobHandler creates a new job handler
func <PERSON>JobHandler(jobService *job.JobService) *JobHandler {
	return &JobHandler{
		jobService: jobService,
	}
}

// CreateJob godoc
// @Summary      Create a new web scraping job
// @Description  Create a new asynchronous web scraping job with comprehensive configuration options. Jobs are queued and processed based on priority.
// @Description
// @Description  **Scraping Capabilities:**
// @Description  - HTTP client for static content (fast)
// @Description  - Headless Chrome for JavaScript-heavy sites
// @Description  - Custom selectors for targeted data extraction
// @Description  - Configurable delays and timeouts
// @Description  - User-agent and header customization
// @Description
// @Description  **Content Extraction:**
// @Description  - Headings (H1-H6), paragraphs, links, images
// @Description  - Tables, forms, lists, structured data
// @Description  - Custom CSS selector-based extraction
// @Description
// @Description  **Job Management:**
// @Description  - Priority-based queue processing
// @Description  - Progress tracking and status updates
// @Description  - Comprehensive result storage in database
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        job body model.CreateJobRequest true "Job configuration including URL, selectors, and scraping options"
// @Success      201 {object} model.Job "Successfully created job - returns job details with unique ID"
// @Failure      400 {object} ErrorResponse "Invalid request format, missing required fields, or invalid configuration"
// @Failure      401 {object} ErrorResponse "User not authenticated or invalid token"
// @Failure      500 {object} ErrorResponse "Internal server error during job creation"
// @Router       /jobs [post]
func (jh *JobHandler) CreateJob(c *gin.Context) {
	var req model.CreateJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	job, err := jh.jobService.CreateJob(userEmail.(string), req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusCreated, job)
}

// GetJob godoc
// @Summary      Get a specific job
// @Description  Retrieve detailed information about a specific scraping job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id path string true "Job ID"
// @Success      200 {object} model.Job
// @Failure      401 {object} ErrorResponse
// @Failure      404 {object} ErrorResponse
// @Router       /jobs/{id} [get]
func (jh *JobHandler) GetJob(c *gin.Context) {
	jobID := c.Param("id")
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	job, err := jh.jobService.GetJob(jobID, userEmail.(string))
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, job)
}

// GetJobs godoc
// @Summary      Get user's scraping jobs
// @Description  Retrieve a paginated list of the current user's scraping jobs with filtering and sorting options.
// @Description
// @Description  **Response includes:**
// @Description  - Job summary information (ID, name, status, progress)
// @Description  - Creation and completion timestamps
// @Description  - Result counts and success rates
// @Description  - Pagination metadata
// @Description
// @Description  **Job Statuses:**
// @Description  - `pending`: Job is queued and waiting to start
// @Description  - `running`: Job is currently being processed
// @Description  - `completed`: Job finished successfully
// @Description  - `failed`: Job encountered an error
// @Description  - `cancelled`: Job was cancelled by user or admin
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        page query int false "Page number (1-based)" default(1) minimum(1)
// @Param        limit query int false "Number of items per page" default(10) minimum(1) maximum(100)
// @Param        status query string false "Filter by job status" Enums(pending,running,completed,failed,cancelled)
// @Param        sort query string false "Sort order" Enums(created_at,-created_at,name,-name,status,-status) default(-created_at)
// @Success      200 {object} JobListResponse "Successfully retrieved jobs list with pagination"
// @Failure      400 {object} ErrorResponse "Invalid query parameters"
// @Failure      401 {object} ErrorResponse "User not authenticated or invalid token"
// @Failure      500 {object} ErrorResponse "Internal server error during job retrieval"
// @Router       /jobs [get]
func (jh *JobHandler) GetJobs(c *gin.Context) {
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	// Parse pagination parameters
	page := 1
	if p := c.Query("page"); p != "" {
		if parsed, err := strconv.Atoi(p); err == nil && parsed > 0 {
			page = parsed
		}
	}

	limit := 10
	if l := c.Query("limit"); l != "" {
		if parsed, err := strconv.Atoi(l); err == nil && parsed > 0 && parsed <= 100 {
			limit = parsed
		}
	}

	offset := (page - 1) * limit

	jobs, total, err := jh.jobService.GetUserJobs(userEmail.(string), offset, limit)
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	response := JobListResponse{
		Jobs:       jobs,
		Total:      total,
		Page:       page,
		Limit:      limit,
		TotalPages: (total + limit - 1) / limit,
	}

	c.JSON(http.StatusOK, response)
}

// UpdateJob godoc
// @Summary      Update a job
// @Description  Update configuration of a pending scraping job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id path string true "Job ID"
// @Param        job body model.UpdateJobRequest true "Updated job configuration"
// @Success      200 {object} model.Job
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      404 {object} ErrorResponse
// @Router       /jobs/{id} [put]
func (jh *JobHandler) UpdateJob(c *gin.Context) {
	jobID := c.Param("id")
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	var req model.UpdateJobRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	job, err := jh.jobService.UpdateJob(jobID, userEmail.(string), req)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, job)
}

// CancelJob godoc
// @Summary      Cancel a job
// @Description  Cancel a pending, queued, or running scraping job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id path string true "Job ID"
// @Success      200 {object} MessageResponse
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      404 {object} ErrorResponse
// @Router       /jobs/{id}/cancel [post]
func (jh *JobHandler) CancelJob(c *gin.Context) {
	jobID := c.Param("id")
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	err := jh.jobService.CancelJob(jobID, userEmail.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{Message: "Job cancelled successfully"})
}

// DeleteJob godoc
// @Summary      Delete a job
// @Description  Delete a completed, failed, or cancelled scraping job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id path string true "Job ID"
// @Success      200 {object} MessageResponse
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Failure      404 {object} ErrorResponse
// @Router       /jobs/{id} [delete]
func (jh *JobHandler) DeleteJob(c *gin.Context) {
	jobID := c.Param("id")
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	err := jh.jobService.DeleteJob(jobID, userEmail.(string))
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, MessageResponse{Message: "Job deleted successfully"})
}

// GetJobStats godoc
// @Summary      Get job statistics
// @Description  Retrieve statistics about user's scraping jobs
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Success      200 {object} model.JobStats
// @Failure      401 {object} ErrorResponse
// @Failure      500 {object} ErrorResponse
// @Router       /jobs/stats [get]
func (jh *JobHandler) GetJobStats(c *gin.Context) {
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	stats, err := jh.jobService.GetJobStats(userEmail.(string))
	if err != nil {
		c.JSON(http.StatusInternalServerError, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, stats)
}

// GetQueuePosition godoc
// @Summary      Get job queue position
// @Description  Get the current position of a job in the execution queue
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id path string true "Job ID"
// @Success      200 {object} QueuePositionResponse
// @Failure      401 {object} ErrorResponse
// @Failure      404 {object} ErrorResponse
// @Router       /jobs/{id}/queue-position [get]
func (jh *JobHandler) GetQueuePosition(c *gin.Context) {
	jobID := c.Param("id")
	
	position, err := jh.jobService.GetQueuePosition(jobID)
	if err != nil {
		c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, QueuePositionResponse{
		JobID:    jobID,
		Position: position,
	})
}

// GetJobResults godoc
// @Summary      Get job scraping results
// @Description  Retrieve comprehensive results from a completed scraping job with pagination support for large datasets.
// @Description
// @Description  **Result Structure:**
// @Description  - **Headings**: H1-H6 tags with text and hierarchy
// @Description  - **Paragraphs**: All paragraph text content
// @Description  - **Links**: URLs, anchor text, titles, and targets
// @Description  - **Images**: URLs, alt text, dimensions
// @Description  - **Tables**: Headers and row data
// @Description  - **Forms**: Field types, names, and validation rules
// @Description  - **Lists**: Ordered and unordered list items
// @Description  - **Structured Data**: JSON-LD, microdata, RDFa
// @Description  - **Custom Selectors**: User-defined CSS selector results
// @Description
// @Description  **Metadata Included:**
// @Description  - Page load times and performance metrics
// @Description  - JavaScript execution status
// @Description  - HTTP response headers and status codes
// @Description  - Error messages and retry information
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        id path string true "Job ID (unique identifier)"
// @Param        page query int false "Page number for paginated results" default(1) minimum(1)
// @Param        limit query int false "Number of results per page" default(50) minimum(1) maximum(1000)
// @Param        format query string false "Response format" Enums(json,csv,xlsx) default(json)
// @Success      200 {object} model.ScrapingResult "Successfully retrieved job results with extracted content"
// @Failure      400 {object} ErrorResponse "Invalid job ID or query parameters"
// @Failure      401 {object} ErrorResponse "User not authenticated or no access to this job"
// @Failure      404 {object} ErrorResponse "Job not found or no results available"
// @Failure      500 {object} ErrorResponse "Internal server error during result retrieval"
// @Router       /jobs/{id}/results [get]
func (jh *JobHandler) GetJobResults(c *gin.Context) {
	jobID := c.Param("id")
	userEmail, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	// Parse pagination parameters
	limit := 100 // default limit
	offset := 0  // default offset

	if limitStr := c.Query("limit"); limitStr != "" {
		if parsedLimit, err := strconv.Atoi(limitStr); err == nil && parsedLimit > 0 {
			limit = parsedLimit
		}
	}

	if offsetStr := c.Query("offset"); offsetStr != "" {
		if parsedOffset, err := strconv.Atoi(offsetStr); err == nil && parsedOffset >= 0 {
			offset = parsedOffset
		}
	}

	results, err := jh.jobService.GetJobResults(c.Request.Context(), jobID, userEmail.(string), limit, offset)
	if err != nil {
		if err.Error() == "job not found" || err.Error() == "unauthorized access to job" {
			c.JSON(http.StatusNotFound, ErrorResponse{Error: err.Error()})
		} else {
			c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		}
		return
	}

	c.JSON(http.StatusOK, results)
}

// TestScrapeURL godoc
// @Summary      Test scrape a single URL
// @Description  Test scraping functionality on a single URL without creating a job
// @Tags         jobs
// @Accept       json
// @Produce      json
// @Security     BearerAuth
// @Param        request body TestScrapeRequest true "URL and selectors to test"
// @Success      200 {object} object "Scraping test results"
// @Failure      400 {object} ErrorResponse
// @Failure      401 {object} ErrorResponse
// @Router       /jobs/test-scrape [post]
func (jh *JobHandler) TestScrapeURL(c *gin.Context) {
	var req TestScrapeRequest
	if err := c.ShouldBindJSON(&req); err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	_, exists := c.Get("email")
	if !exists {
		c.JSON(http.StatusUnauthorized, ErrorResponse{Error: "User not authenticated"})
		return
	}

	// Create a test scrape request
	testReq := job.TestScrapeRequest{
		URL: req.URL,
	}

	result, err := jh.jobService.TestScrapeURL(testReq)
	if err != nil {
		c.JSON(http.StatusBadRequest, ErrorResponse{Error: err.Error()})
		return
	}

	c.JSON(http.StatusOK, result)
}

// Response types for API documentation

// JobListResponse represents a paginated list of jobs
type JobListResponse struct {
	Jobs       []*model.JobSummary `json:"jobs"`
	Total      int                 `json:"total" example:"150"`
	Page       int                 `json:"page" example:"1"`
	Limit      int                 `json:"limit" example:"10"`
	TotalPages int                 `json:"total_pages" example:"15"`
}

// MessageResponse represents a simple message response
type MessageResponse struct {
	Message string `json:"message" example:"Operation completed successfully"`
}

// QueuePositionResponse represents the queue position of a job
type QueuePositionResponse struct {
	JobID    string `json:"job_id" example:"job_123456"`
	Position int    `json:"position" example:"3"`
}

// TestScrapeRequest represents a request to test scraping a URL
type TestScrapeRequest struct {
	URL               string            `json:"url" binding:"required" example:"https://example.com"`
	Selectors         map[string]string `json:"selectors,omitempty" example:"{\"title\":\"h1\",\"content\":\"p\"}"`
	JavaScriptEnabled bool              `json:"javascript_enabled" example:"false"`
	ComprehensiveMode bool              `json:"comprehensive_mode" example:"true"` // Extract all content automatically
}
