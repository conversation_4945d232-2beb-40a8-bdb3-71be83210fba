package monitoring

import (
	"context"
	"runtime"
	"time"

	"go-rest-api/config"
	"go-rest-api/internal/model"
	"go-rest-api/internal/repository"

	"github.com/sirupsen/logrus"
)

// MonitoringService handles system monitoring and health checks
type MonitoringService struct {
	repos *repository.Repositories
}

// NewMonitoringService creates a new MonitoringService
func NewMonitoringService(repos *repository.Repositories) *MonitoringService {
	return &MonitoringService{
		repos: repos,
	}
}

// SystemMetrics represents system performance metrics
type SystemMetrics struct {
	Timestamp        time.Time `json:"timestamp"`
	CPUUsage         float64   `json:"cpu_usage"`
	MemoryUsage      uint64    `json:"memory_usage_bytes"`
	MemoryUsageMB    float64   `json:"memory_usage_mb"`
	GoroutineCount   int       `json:"goroutine_count"`
	HeapSize         uint64    `json:"heap_size_bytes"`
	HeapSizeMB       float64   `json:"heap_size_mb"`
	GCPauseTotal     uint64    `json:"gc_pause_total_ns"`
	DatabaseConnections int    `json:"database_connections"`
}

// HealthStatus represents the health status of the system
type HealthStatus struct {
	Status      string                 `json:"status" example:"healthy"`
	Timestamp   time.Time              `json:"timestamp" example:"2024-07-29T15:44:02Z"`
	Version     string                 `json:"version" example:"1.0.0"`
	Uptime      string                 `json:"uptime" example:"2h30m45s"`
	Checks      map[string]CheckResult `json:"checks"`
	Metrics     SystemMetrics          `json:"metrics"`
}

// CheckResult represents the result of a health check
type CheckResult struct {
	Status  string `json:"status" example:"healthy"`
	Message string `json:"message,omitempty" example:"Database connection successful"`
	Latency string `json:"latency,omitempty" example:"5ms"`
}

var startTime = time.Now()

// GetHealthStatus returns the current health status of the system
func (s *MonitoringService) GetHealthStatus() *HealthStatus {
	checks := make(map[string]CheckResult)

	// Database health check
	dbCheck := s.checkDatabase()
	checks["database"] = dbCheck

	// Memory health check
	memCheck := s.checkMemory()
	checks["memory"] = memCheck

	// Determine overall status
	overallStatus := "healthy"
	for _, check := range checks {
		if check.Status != "healthy" {
			overallStatus = "unhealthy"
			break
		}
	}

	return &HealthStatus{
		Status:    overallStatus,
		Timestamp: time.Now(),
		Version:   "1.0.0", // This should come from build info
		Uptime:    time.Since(startTime).String(),
		Checks:    checks,
		Metrics:   s.getSystemMetrics(),
	}
}

// checkDatabase performs a database health check
func (s *MonitoringService) checkDatabase() CheckResult {
	start := time.Now()
	
	// Try to get a count from a simple table
	_, err := s.repos.User.Count(context.Background())
	latency := time.Since(start)

	if err != nil {
		return CheckResult{
			Status:  "unhealthy",
			Message: "Database connection failed: " + err.Error(),
			Latency: latency.String(),
		}
	}

	return CheckResult{
		Status:  "healthy",
		Message: "Database connection successful",
		Latency: latency.String(),
	}
}

// checkMemory performs a memory usage health check
func (s *MonitoringService) checkMemory() CheckResult {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	// Consider unhealthy if using more than 1GB of memory
	const maxMemoryMB = 1024
	memoryMB := float64(m.Alloc) / 1024 / 1024

	if memoryMB > maxMemoryMB {
		return CheckResult{
			Status:  "unhealthy",
			Message: "High memory usage",
		}
	}

	return CheckResult{
		Status:  "healthy",
		Message: "Memory usage normal",
	}
}

// getSystemMetrics collects current system metrics
func (s *MonitoringService) getSystemMetrics() SystemMetrics {
	var m runtime.MemStats
	runtime.ReadMemStats(&m)

	return SystemMetrics{
		Timestamp:      time.Now(),
		MemoryUsage:    m.Alloc,
		MemoryUsageMB:  float64(m.Alloc) / 1024 / 1024,
		GoroutineCount: runtime.NumGoroutine(),
		HeapSize:       m.HeapAlloc,
		HeapSizeMB:     float64(m.HeapAlloc) / 1024 / 1024,
		GCPauseTotal:   m.PauseTotalNs,
	}
}

// LogSystemEvent logs a system event to the database
func (s *MonitoringService) LogSystemEvent(level, component, message string, details interface{}) error {
	systemLog := &model.SystemLog{
		Level:     level,
		Component: component,
		Message:   message,
		Details:   details,
		CreatedAt: time.Now(),
	}

	// Log to database
	if err := s.repos.Audit.CreateSystemLog(context.Background(), systemLog); err != nil {
		// If database logging fails, at least log to the application logger
		config.WithFields(logrus.Fields{
			"level":     level,
			"component": component,
			"details":   details,
			"error":     err.Error(),
		}).Error("Failed to log system event to database")
		return err
	}

	// Also log to application logger
	config.WithFields(logrus.Fields{
		"component": component,
		"details":   details,
		"type":      "system_event",
	}).Log(config.Logger.Level, message)

	return nil
}

// StartMetricsCollection starts periodic collection of system metrics
func (s *MonitoringService) StartMetricsCollection(interval time.Duration) {
	ticker := time.NewTicker(interval)
	go func() {
		for range ticker.C {
			metrics := s.getSystemMetrics()
			
			// Log metrics periodically
			config.WithFields(logrus.Fields{
				"memory_mb":       metrics.MemoryUsageMB,
				"goroutines":      metrics.GoroutineCount,
				"heap_mb":         metrics.HeapSizeMB,
				"type":            "metrics",
			}).Debug("System Metrics")

			// Log system event for high memory usage
			if metrics.MemoryUsageMB > 512 { // 512MB threshold
				s.LogSystemEvent("warn", "monitoring", "High memory usage detected", map[string]interface{}{
					"memory_mb": metrics.MemoryUsageMB,
					"heap_mb":   metrics.HeapSizeMB,
				})
			}

			// Log system event for high goroutine count
			if metrics.GoroutineCount > 1000 {
				s.LogSystemEvent("warn", "monitoring", "High goroutine count detected", map[string]interface{}{
					"goroutine_count": metrics.GoroutineCount,
				})
			}
		}
	}()
}

// CleanupOldLogs removes old log entries based on retention policy
func (s *MonitoringService) CleanupOldLogs(auditRetentionDays, systemRetentionDays int) error {
	// Clean up old audit logs
	auditRetention := time.Duration(auditRetentionDays) * 24 * time.Hour
	if err := s.repos.Audit.DeleteOldAuditLogs(context.Background(), auditRetention); err != nil {
		config.WithError(err).Error("Failed to cleanup old audit logs")
		return err
	}

	// Clean up old system logs
	systemRetention := time.Duration(systemRetentionDays) * 24 * time.Hour
	if err := s.repos.Audit.DeleteOldSystemLogs(context.Background(), systemRetention); err != nil {
		config.WithError(err).Error("Failed to cleanup old system logs")
		return err
	}

	config.WithFields(logrus.Fields{
		"audit_retention_days":  auditRetentionDays,
		"system_retention_days": systemRetentionDays,
	}).Info("Log cleanup completed")

	return nil
}
